<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_main">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:background="#a0f0f0f0">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_weight="1">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_weight="1">

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_weight="1">
                <TextView
                    android:id="@+id/wordText"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:text="word"
                    android:textSize="60dp"
                    android:textColor="#000000"
                    android:lineSpacingMultiplier="1"
                    android:textStyle="bold"
                    android:gravity="center_vertical|center_horizontal"
                    android:layout_centerHorizontal="true"/>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/definitionText"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerHorizontal="true"
                    android:layout_weight="1"
                    android:gravity="center_horizontal|fill_vertical"
                    android:lineSpacingMultiplier="1.5"
                    android:text="单词"
                    android:textColor="#000000"
                    android:textSize="25dp" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_weight="1"
            android:layout_marginBottom="55dp"
            >
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_weight="1">

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_weight="1">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:layout_weight="1">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"/>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="horizontal">
                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical">
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:orientation="horizontal">
                            </LinearLayout>
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_weight="1"
                                android:orientation="vertical">
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1.8"
                                    android:orientation="horizontal"/>
                                <ImageButton
                                    android:id="@+id/tipsButton"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_weight="1"
                                    android:background="@drawable/icon_tips"/>
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:layout_weight="1">
                <Button
                        android:id="@+id/knowButton"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="认识"
                        android:textColor="#ff000000"
                        android:textSize="30dp"
                        android:textStyle="bold"
                        android:background="@drawable/blur_button"/>

                <Button
                        android:id="@+id/unknowButton"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="不认识"
                        android:textColor="#ff000000"
                        android:textSize="30dp"
                        android:textStyle="bold"
                        android:background="@drawable/blur_button"/>
            </LinearLayout>
        </LinearLayout>

   </LinearLayout>

</RelativeLayout>