<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/fighLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <com.mingle.widget.LoadingView
                android:id="@+id/loadView"
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:alpha="1"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center|top"
                android:text="匹配中，请稍后..."
                android:textColor="#000000"
                android:textSize="20dp"
                android:textStyle="bold"
                android:alpha="1"/>
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>