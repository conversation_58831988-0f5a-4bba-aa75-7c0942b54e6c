com.example.reciteword.app-navigation-runtime-2.2.1-0 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2889cef771008d21d7036de8aeac9a0c\transformed\navigation-runtime-2.2.1\res
com.example.reciteword.app-appcompat-1.2.0-1 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\res
com.example.reciteword.app-jetified-viewpager2-1.0.0-2 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31bef4511f5078c7c2e6419f95104519\transformed\jetified-viewpager2-1.0.0\res
com.example.reciteword.app-material-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\res
com.example.reciteword.app-jetified-android-shapeLoadingView-1.0.3.2-4 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\386495f72499a137d9685efaac94ea71\transformed\jetified-android-shapeLoadingView-1.0.3.2\res
com.example.reciteword.app-fragment-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6613231746df27feeece63747b3026\transformed\fragment-1.2.0\res
com.example.reciteword.app-constraintlayout-2.0.4-6 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\res
com.example.reciteword.app-navigation-fragment-2.2.0-7 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e01b060be0609e5f1461863b091166c\transformed\navigation-fragment-2.2.0\res
com.example.reciteword.app-transition-1.2.0-8 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\res
com.example.reciteword.app-navigation-ui-2.2.1-9 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8ce575fa6a34354b0500542469c8c7\transformed\navigation-ui-2.2.1\res
com.example.reciteword.app-core-1.3.1-10 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\res
com.example.reciteword.app-jetified-appcompat-resources-1.2.0-11 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dfb217438f3b63eba7e25f6e2373559\transformed\jetified-appcompat-resources-1.2.0\res
com.example.reciteword.app-cardview-1.0.0-12 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\res
com.example.reciteword.app-recyclerview-1.1.0-13 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\res
com.example.reciteword.app-navigation-common-2.2.1-14 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb38460cee26ccc08a426fa7eac1dd47\transformed\navigation-common-2.2.1\res
com.example.reciteword.app-coordinatorlayout-1.1.0-15 C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\res
com.example.reciteword.app-pngs-16 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\generated\res\pngs\debug
com.example.reciteword.app-resValues-17 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\generated\res\resValues\debug
com.example.reciteword.app-packageDebugResources-18 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.reciteword.app-packageDebugResources-19 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.reciteword.app-debug-20 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.reciteword.app-debug-21 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\debug\res
com.example.reciteword.app-main-22 C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res
