<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        package="com.example.reciteword">
    <!-- 文件读写权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
            android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
            tools:ignore="ProtectedPermissions" />

    <application
            android:allowBackup="true"
            android:icon="@drawable/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:supportsRtl="true"
            android:theme="@style/Theme.ReciteWord">
        <activity android:name=".activity.FailActivity">
        </activity>
        <activity android:name=".activity.RegisterActivity" android:launchMode="singleTask"></activity>
        <activity android:name=".activity.LoginActivity" android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".activity.SuccessActivity"></activity>
        <activity android:name=".activity.BattleActivity"></activity>
        <activity android:name=".activity.InterfaceActivity" >
            <intent-filter>
                <!-- 添加一个空的 intent-filter -->
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>
    </application>

</manifest>