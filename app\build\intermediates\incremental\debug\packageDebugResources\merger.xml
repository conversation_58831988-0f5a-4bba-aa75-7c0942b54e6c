<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="pressed_color">#FF3700B3</color><color name="un_clicked_color">#FF6200EE</color><color name="focused_color">#FFFFFFFF</color><color name="shadow_color">#80000000</color><color name="unfocused_color">#FFF5F5F5</color></file><file path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ReciteWord</string></file><file path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.ReciteWord" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="background_main" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\background_main.jpg" qualifiers="" type="drawable"/><file name="blur_button" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\blur_button.xml" qualifiers="" type="drawable"/><file name="blur_button_background" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\blur_button_background.xml" qualifiers="" type="drawable"/><file name="button_background" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="circle" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\circle.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\ic_launcher.PNG" qualifiers="" type="drawable"/><file name="icon_brush" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_brush.png" qualifiers="" type="drawable"/><file name="icon_face1" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_face1.png" qualifiers="" type="drawable"/><file name="icon_face2" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_face2.png" qualifiers="" type="drawable"/><file name="icon_fight" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_fight.png" qualifiers="" type="drawable"/><file name="icon_recite" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_recite.png" qualifiers="" type="drawable"/><file name="icon_tips" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_tips.png" qualifiers="" type="drawable"/><file name="icon_wrong" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\icon_wrong.png" qualifiers="" type="drawable"/><file name="round_frame" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable\round_frame.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="rounded_button" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable-v24\rounded_button.xml" qualifiers="v24" type="drawable"/><file name="rounded_edittext" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\drawable-v24\rounded_edittext.xml" qualifiers="v24" type="drawable"/><file name="activity_fail" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_fail.xml" qualifiers="" type="layout"/><file name="activity_interface" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_interface.xml" qualifiers="" type="layout"/><file name="activity_load" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_load.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_register" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_register.xml" qualifiers="" type="layout"/><file name="activity_success" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\activity_success.xml" qualifiers="" type="layout"/><file name="fragment_brush" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\fragment_brush.xml" qualifiers="" type="layout"/><file name="fragment_fight" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\fragment_fight.xml" qualifiers="" type="layout"/><file name="fragment_recite" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\fragment_recite.xml" qualifiers="" type="layout"/><file name="fragment_wrong" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\fragment_wrong.xml" qualifiers="" type="layout"/><file name="word_item" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\layout\word_item.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.ReciteWord" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\A (1)\android-app-main\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>