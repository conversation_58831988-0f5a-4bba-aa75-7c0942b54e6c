<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item android:right="5dp" android:top="5dp">
                <shape>
                    <corners android:radius="20dp" />
                    <solid android:color="#10CCCCCC" />
                </shape>
            </item>
            <item android:bottom="2dp" android:left="2dp">
                <shape>
                    <gradient android:angle="270"
                            android:endColor="#10CCCCCC" android:startColor="#20CCCCCC" />
                    <stroke android:width="1dp" android:color="#50999999" />
                    <corners android:radius="20dp" />
                    <padding android:bottom="10dp" android:left="10dp"
                            android:right="10dp" android:top="10dp" />
                </shape>
            </item>
        </layer-list>
    </item>

</selector>