<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/background_color">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="登录页面"
                android:textColor="@color/black"
                android:textSize="50sp"
                android:textStyle="bold"
                android:gravity="center"
                android:padding="16dp"
                app:layout_constraintTop_toTopOf="parent"/>

        <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="用户名"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"/>

        <EditText
                android:id="@+id/etUsername"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/edit_text_background"
                android:padding="12dp"
                android:hint="请输入用户名"
                android:layout_marginBottom="16dp"/>

        <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="密码"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:layout_marginBottom="8dp"/>

        <EditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:background="@drawable/edit_text_background"
                android:padding="12dp"
                android:hint="请输入密码"
                android:layout_marginBottom="16dp"/>

        <TextView
                android:id="@+id/tvError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/holo_red_light"
                android:layout_marginBottom="16dp"/>

        <Button
                android:id="@+id/btnLogin"
                android:layout_width="200dp"
                android:layout_gravity="center"
                android:layout_height="wrap_content"
                android:text="登录"
                android:background="@drawable/button_background"
                android:textColor="@android:color/white"
                android:padding="12dp"
                android:layout_marginBottom="16dp"/>

        <TextView
                android:id="@+id/tvToRegister"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="去注册"
                android:textColor="@color/purple_500"
                android:textSize="16sp"
                android:gravity="center"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>