<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/round_frame">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:alpha="1">

            <TextView
                    android:id="@+id/word_context"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.3"
                    android:textStyle="bold"
                    android:textSize="17sp"
                    android:textColor="#000000"
                    android:gravity="center_vertical"
                    android:layout_marginLeft="30dp"
                    android:alpha="1" />

            <TextView
                    android:id="@+id/word_definition"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:textSize="16sp"
                    android:textColor="#000000"
                    android:gravity="center_vertical"
                    android:alpha="1" />
        </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:alpha="1">

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1.3">

                <TextView
                        android:id="@+id/word_pron"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:textStyle="bold"
                        android:textSize="17sp"
                        android:textColor="#000000"
                        android:gravity="center_vertical"
                        android:layout_marginLeft="30dp"
                        android:alpha="1" />
            </LinearLayout>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1">
                <TextView
                        android:id="@+id/wordItemText"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="出现次数:"
                        android:textSize="16sp"
                        android:textColor="#000000"
                        android:gravity="center_vertical"
                        android:alpha="1" />
                <TextView
                        android:id="@+id/word_flag"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:textStyle="bold"
                        android:textSize="17sp"
                        android:textColor="#FF2196F3"
                        android:gravity="center_vertical"
                        android:alpha="1" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>