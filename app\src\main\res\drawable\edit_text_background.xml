<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/focused_color"/>
            <corners android:radius="8dp"/>
            <shadow android:color="@color/shadow_color" android:radius="4dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/unfocused_color"/>
            <corners android:radius="8dp"/>
            <shadow android:color="@color/shadow_color" android:radius="4dp" />
        </shape>
    </item>
</selector>