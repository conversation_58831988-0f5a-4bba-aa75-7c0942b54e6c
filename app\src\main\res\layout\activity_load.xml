<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_main">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:background="#a0f0f0f0">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="horizontal">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="10dp"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_face1" />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/user1Score"
                        android:layout_marginTop="20dp"
                        android:layout_marginLeft="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="得分："
                        android:textSize="25dp"
                        android:textStyle="bold"
                        android:gravity="left"
                        android:layout_centerHorizontal="true"/>
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:orientation="horizontal">
                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="10dp"
                            android:layout_gravity="center"
                            android:src="@drawable/icon_face2" />
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical">
                    <TextView
                        android:id="@+id/user2Score"
                        android:layout_marginTop="20dp"
                        android:layout_marginRight="10dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="得分："
                        android:textSize="25dp"
                        android:textStyle="bold"
                        android:gravity="right"
                        android:layout_centerHorizontal="true"/>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/wordFightText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="word"
                android:textSize="60dp"
                android:textStyle="bold"
                android:gravity="center_vertical|center_horizontal"
                android:layout_centerHorizontal="true"/>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <Button
                android:id="@+id/choose1Button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="认识"
                android:textColor="#ff000000"
                android:textSize="20dp"
                android:textStyle="bold"
                android:background="@drawable/blur_button"/>
            <Button
                android:id="@+id/choose2Button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="认识"
                android:textColor="#ff000000"
                android:textSize="20dp"
                android:textStyle="bold"
                android:background="@drawable/blur_button"/>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <Button
                android:id="@+id/choose3Button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:text="认识"
                android:textColor="#ff000000"
                android:textSize="20dp"
                android:textStyle="bold"
                android:background="@drawable/blur_button"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:orientation="vertical"/>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>