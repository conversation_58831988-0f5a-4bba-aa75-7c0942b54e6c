-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:2:1-19:12
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:2:1-19:12
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:2:1-19:12
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:2:1-19:12
MERGED from [com.github.zzz40500:android-shapeLoadingView:1.0.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\386495f72499a137d9685efaac94ea71\transformed\jetified-android-shapeLoadingView-1.0.3.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.navigation:navigation-ui:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8ce575fa6a34354b0500542469c8c7\transformed\navigation-ui-2.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-fragment:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e01b060be0609e5f1461863b091166c\transformed\navigation-fragment-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dfb217438f3b63eba7e25f6e2373559\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31bef4511f5078c7c2e6419f95104519\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6613231746df27feeece63747b3026\transformed\fragment-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2889cef771008d21d7036de8aeac9a0c\transformed\navigation-runtime-2.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d510e8016c76569ddf5553acfa840c2\transformed\jetified-activity-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb38460cee26ccc08a426fa7eac1dd47\transformed\navigation-common-2.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a69ee11a06891f831587b78ad8eeb60\transformed\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\669f1ff6a6e5e901b63148b925be1525\transformed\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b224977d226b2e786b49734983dce4c\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e27e162a7d5f9ecffe8fa2e022e0e34\transformed\lifecycle-runtime-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9169b21bdb4cb664aaea328a1324caf3\transformed\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d03fc43f139de516b59b79981b72353\transformed\jetified-annotation-experimental-1.0.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:4:5-17:19
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:4:5-17:19
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:24:18-86
	android:label
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:7:9-41
	android:icon
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:6:9-56
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:8:9-48
activity#com.example.reciteword.MainActivity
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:9:9-16:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:11:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:10:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:12:13-15:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:13:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:13:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:14:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml:14:27-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
MERGED from [com.github.zzz40500:android-shapeLoadingView:1.0.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\386495f72499a137d9685efaac94ea71\transformed\jetified-android-shapeLoadingView-1.0.3.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.zzz40500:android-shapeLoadingView:1.0.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\386495f72499a137d9685efaac94ea71\transformed\jetified-android-shapeLoadingView-1.0.3.2\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.navigation:navigation-ui:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8ce575fa6a34354b0500542469c8c7\transformed\navigation-ui-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-ui:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a8ce575fa6a34354b0500542469c8c7\transformed\navigation-ui-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32174b9aa61ba085e525e198023dc11d\transformed\material-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5df0cc37f3897eb7ce7bbd0645a646ac\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\313b7d5dbe5dc9219fc547695506f52e\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e01b060be0609e5f1461863b091166c\transformed\navigation-fragment-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-fragment:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e01b060be0609e5f1461863b091166c\transformed\navigation-fragment-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b2f03a6c34802a46a45d6399c7ae921\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dfb217438f3b63eba7e25f6e2373559\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8dfb217438f3b63eba7e25f6e2373559\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd4d41628e5e0c081b523aaadd165524\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a60c159c7ca9eb3593d57ef6199673cc\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7e9ae46787c47ae351e8a61be234d65\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71ea803259349f97814933245451a8d3\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3333dea171f507cf27a6711021ecc98\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c669eb0ac81dfb09b80a2425fba5a6f3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31bef4511f5078c7c2e6419f95104519\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31bef4511f5078c7c2e6419f95104519\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6613231746df27feeece63747b3026\transformed\fragment-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f6613231746df27feeece63747b3026\transformed\fragment-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bef938e02d3b77a5a565924eddcd5232\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7a17fcb820eb6f9d68b4346d0296d65d\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c0b595a199549f3e428240151565589\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\084a750bd95de8b03c30a62db7c865b0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2889cef771008d21d7036de8aeac9a0c\transformed\navigation-runtime-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2889cef771008d21d7036de8aeac9a0c\transformed\navigation-runtime-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d510e8016c76569ddf5553acfa840c2\transformed\jetified-activity-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d510e8016c76569ddf5553acfa840c2\transformed\jetified-activity-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb38460cee26ccc08a426fa7eac1dd47\transformed\navigation-common-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb38460cee26ccc08a426fa7eac1dd47\transformed\navigation-common-2.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d06f4c23c6b78afc6b8e18c56daf21c\transformed\core-1.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ede02a27188e30128c4e6e8452cf3ac9\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a69ee11a06891f831587b78ad8eeb60\transformed\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a69ee11a06891f831587b78ad8eeb60\transformed\jetified-lifecycle-viewmodel-savedstate-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\669f1ff6a6e5e901b63148b925be1525\transformed\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\669f1ff6a6e5e901b63148b925be1525\transformed\lifecycle-viewmodel-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b224977d226b2e786b49734983dce4c\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b224977d226b2e786b49734983dce4c\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cc7dfd2c0fcae0982c28937b18f49ab\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e27e162a7d5f9ecffe8fa2e022e0e34\transformed\lifecycle-runtime-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e27e162a7d5f9ecffe8fa2e022e0e34\transformed\lifecycle-runtime-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0465b0c41e232f9758bbbe67283f8116\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9169b21bdb4cb664aaea328a1324caf3\transformed\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9169b21bdb4cb664aaea328a1324caf3\transformed\lifecycle-livedata-core-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\017843d23ff14032b1831a1cd6fb2431\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d03fc43f139de516b59b79981b72353\transformed\jetified-annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d03fc43f139de516b59b79981b72353\transformed\jetified-annotation-experimental-1.0.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\A (1)\android-app-main\app\src\main\AndroidManifest.xml
