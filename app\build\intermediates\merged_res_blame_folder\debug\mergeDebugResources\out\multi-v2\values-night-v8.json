{"logs": [{"outputFile": "com.example.reciteword.app-mergeDebugResources-18:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\32174b9aa61ba085e525e198023dc11d\\transformed\\material-1.2.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,39,40,41,42,43,44,45", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,820,944,1046,1148,1264,1366,1480,1608,1724,1846,1982,2102,2236,2356,2468,3284,3422,3546,3676,3798,3936,4052", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,137,123,129,121,137,115,119", "endOffsets": "815,939,1041,1143,1259,1361,1475,1603,1719,1841,1977,2097,2231,2351,2463,2589,3417,3541,3671,3793,3931,4047,4167"}}, {"source": "C:\\Users\\<USER>\\Desktop\\A (1)\\android-app-main\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "14", "endColumns": "12", "endOffsets": "819"}, "to": {"startLines": "25", "startColumns": "4", "startOffsets": "2594", "endLines": "37", "endColumns": "12", "endOffsets": "3190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\313b7d5dbe5dc9219fc547695506f52e\\transformed\\appcompat-1.2.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,38", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "100,170,254,338,434,536,638,3195", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "165,249,333,429,531,633,727,3279"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.example.reciteword.app-mergeDebugResources-18:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\32174b9aa61ba085e525e198023dc11d\\transformed\\material-1.2.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,267,369,471,587,689,803,931,1047,1169,1305,1425,1559,1679,1791,1917,2055,2179,2309,2431,2569,2685", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,137,123,129,121,137,115,119", "endOffsets": "138,262,364,466,582,684,798,926,1042,1164,1300,1420,1554,1674,1786,1912,2050,2174,2304,2426,2564,2680,2800"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,27,28,29,30,31,32", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "687,775,899,1001,1103,1219,1321,1435,1563,1679,1801,1937,2057,2191,2311,2423,2638,2776,2900,3030,3152,3290,3406", "endColumns": "87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,137,123,129,121,137,115,119", "endOffsets": "770,894,996,1098,1214,1316,1430,1558,1674,1796,1932,2052,2186,2306,2418,2544,2771,2895,3025,3147,3285,3401,3521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\313b7d5dbe5dc9219fc547695506f52e\\transformed\\appcompat-1.2.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "2,3,4,5,6,7,8,25", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,2549", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,2633"}}]}]}